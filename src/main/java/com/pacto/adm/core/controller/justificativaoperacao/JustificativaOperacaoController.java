package com.pacto.adm.core.controller.justificativaoperacao;

import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.services.interfaces.justificativaoperacao.JustificativaOperacaoService;
import com.pacto.adm.core.swagger.respostas.justificativaoperacao.ExemploRespostaListJustificativaOperacaoPaginacao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/justificativa-operacao")
public class JustificativaOperacaoController {

    private final JustificativaOperacaoService justificativaOperacaoService;

    public JustificativaOperacaoController(JustificativaOperacaoService justificativaOperacaoService) {
        this.justificativaOperacaoService = justificativaOperacaoService;
    }

    @GetMapping("/contratos-cancelados-transferidos")
    public ResponseEntity<EnvelopeRespostaDTO> consultarContratosCancelados(
            @RequestParam(value = "filters", required = false) String filtros,
            PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            return ResponseEntityFactory.ok(justificativaOperacaoService.contratosCanceladosTransferidosOutroAluno(filtroBIControleOperacoesJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
